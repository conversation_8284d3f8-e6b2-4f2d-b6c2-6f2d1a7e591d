{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Projeto Carbon - Comgás\n", "## Sistema de PLN para Análise de Qualidade de Atendimento\n", "\n", "Este notebook implementa o modelo de PLN que analisa transcrições de ligações telefônicas da Comgás conforme especificado na documentação do projeto."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Instalação das dependências necessárias\n", "!pip install unidecode nltk scikit-learn transformers sentence-transformers torch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importações necessárias\n", "import re\n", "import unidecode\n", "import nltk\n", "import torch\n", "import numpy as np\n", "from nltk.tokenize import sent_tokenize, word_tokenize\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from transformers import AutoTokenizer, AutoModelForSequenceClassification\n", "from sentence_transformers import SentenceTransformer, util\n", "from sklearn.linear_model import LogisticRegression\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Download dos recursos do NLTK\n", "nltk.download(\"punkt\")\n", "nltk.download(\"punkt_tab\")\n", "\n", "print(\"✅ Bibliotecas importadas e recursos baixados com sucesso!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Pré-processamento de Dados\n", "\n", "Funções para limpar e estruturar as transcrições conforme especificado no projeto."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def preprocessar_geral(texto: str) -> dict:\n", "    \"\"\"\n", "    Normaliza e limpa o texto para análise de PLN.\n", "    Baseado nas especificações do projeto Carbon.\n", "    \"\"\"\n", "    # 1. Normalização básica\n", "    t = texto.lower().strip()\n", "    t = unidecode.unidecode(t)   # remove acentos para padronização\n", "\n", "    # 2. Remoção de caracteres especiais redundantes\n", "    t = re.sub(r\"[^a-z0-9áéíóúãõç\\s\\.\\,\\!\\?]\", \" \", t)\n", "    t = re.sub(r\"\\s+\", \" \", t).strip()\n", "\n", "    # 3. Tokenização em sentenças e palavras\n", "    sentencas = sent_tokenize(t, language=\"portuguese\")\n", "    tokens = [word_tokenize(s, language=\"portuguese\") for s in sentencas]\n", "\n", "    # 4. <PERSON><PERSON>da organizada\n", "    return {\n", "        \"texto_limpo\": t,\n", "        \"sentencas\": sentencas,\n", "        \"tokens_por_sentenca\": tokens\n", "    }\n", "\n", "def preprocessar_conversa(conversa: list) -> list:\n", "    \"\"\"\n", "    Processa uma conversa completa no formato:\n", "    [00:00] PESSOA: texto da fala\n", "    \n", "    Retorna lista estruturada com timestamps e análises.\n", "    \"\"\"\n", "    dados = []\n", "    padrao = re.compile(r\"\\[(\\d{2}:\\d{2})\\]\\s*([A-ZÇÃÕÉÍÓÚ]+):\\s*(.*)\")\n", "\n", "    for linha in conversa:\n", "        m = padrao.match(linha)\n", "        if not m:\n", "            continue  # ignora linhas fora do formato esperado\n", "        timestamp, speaker, fala = m.groups()\n", "        dados.append({\n", "            \"timestamp\": timestamp,\n", "            \"speaker\": speaker.title(),  # normaliza para \"Pessoa\"\n", "            \"texto_original\": fala,\n", "            \"preprocessado\": preprocessar_geral(fala)\n", "        })\n", "\n", "    return dados\n", "\n", "print(\"✅ Funções de pré-processamento definidas!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Carregamento dos Modelos de PLN\n", "\n", "Inicialização dos modelos conforme especificado na documentação do projeto."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inicialização dos modelos e vectorizers\n", "print(\"🔄 Carregando modelos...\")\n", "\n", "# TF-IDF Vectorizer para análises baseadas em frequência\n", "tfidf = TfidfVectorizer(ngram_range=(1,2))\n", "\n", "# BERTimbau para análises contextuais\n", "bert_tokenizer = AutoTokenizer.from_pretrained(\"neuralmind/bert-base-portuguese-cased\")\n", "# Nota: O modelo será carregado quando necessário para economizar memória\n", "\n", "# Sentence Transformer para similaridade semântica\n", "sbert = SentenceTransformer(\"paraphrase-multilingual-MiniLM-L12-v2\")\n", "\n", "# Classificadores que serão treinados\n", "clf_hold = LogisticRegression()\n", "clf_sondagem = LogisticRegression()\n", "\n", "print(\"✅ Modelos carregados com sucesso!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Funções auxiliares para processamento de timestamps e preparação de dados."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def to_seconds(ts_mmss: str) -> int:\n", "    \"\"\"\n", "    Converte string 'MM:SS' para inteiro em segundos.\n", "    Ex.: '01:23' -> 83\n", "    \"\"\"\n", "    mm, ss = ts_mmss.split(\":\")\n", "    return int(mm) * 60 + int(ss)\n", "\n", "def preprocessar_tfidf(fala: dict):\n", "    \"\"\"Prepara texto para análise TF-IDF\"\"\"\n", "    return tfidf.fit_transform([fala[\"preprocessado\"][\"texto_limpo\"]])\n", "\n", "def preprocessar_bert(fala: dict):\n", "    \"\"\"Prepara texto para análise com BERTimbau\"\"\"\n", "    return bert_tokenizer(\n", "        fala[\"preprocessado\"][\"texto_limpo\"],\n", "        return_tensors=\"pt\",\n", "        truncation=True,\n", "        padding=True\n", "    )\n", "\n", "def preprocessar_embeddings(fala: dict):\n", "    \"\"\"Prepara texto para análise de embeddings\"\"\"\n", "    return sbert.encode([fala[\"preprocessado\"][\"texto_limpo\"]], normalize_embeddings=True)\n", "\n", "def preprocessar_timestamps(fala: dict):\n", "    \"\"\"Extrai informações de timestamp\"\"\"\n", "    return {\n", "        \"timestamp\": fala.get(\"timestamp\", None),\n", "        \"texto\": fala[\"preprocessado\"][\"texto_limpo\"]\n", "    }\n", "\n", "print(\"✅ Funções utilitárias definidas!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Funções de Análise de PLN\n", "\n", "Implementação das análises específicas conforme documentação do projeto Carbon."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ========== FUNÇÕES DE APLICAÇÃO DOS ALGORITMOS ==========\n", "\n", "def aplicar_regex(fala: dict, padrao: list) -> bool:\n", "    \"\"\"Aplica padrões regex no texto\"\"\"\n", "    texto = fala[\"preprocessado\"][\"texto_limpo\"]\n", "    return any(re.search(p, texto) for p in padrao)\n", "\n", "def aplicar_tfidf(fala: dict, clf, vectorizer) -> str:\n", "    \"\"\"Aplica classificação TF-IDF\"\"\"\n", "    texto = fala[\"preprocessado\"][\"texto_limpo\"]\n", "    X = vectorizer.transform([texto])\n", "    return clf.predict(X)[0]\n", "\n", "def aplicar_bert(fala: dict, model, tokenizer, limiar=0.5) -> bool:\n", "    \"\"\"Aplica análise com BERTimbau\"\"\"\n", "    texto = fala[\"preprocessado\"][\"texto_limpo\"]\n", "    inputs = tokenizer(texto, return_tensors=\"pt\", truncation=True, padding=True)\n", "    with torch.no_grad():\n", "        probs = torch.softmax(model(**inputs).logits, dim=-1)[0]\n", "    return probs[1].item() >= limiar\n", "\n", "def aplicar_embeddings(fala: dict, templates, limiar=0.7) -> bool:\n", "    \"\"\"Aplica análise de similaridade com embeddings\"\"\"\n", "    emb_fala = sbert.encode([fala[\"preprocessado\"][\"texto_limpo\"]], normalize_embeddings=True)\n", "    sims = util.cos_sim(emb_fala, templates).cpu().numpy()[0]\n", "    return float(np.max(sims)) >= limiar\n", "\n", "print(\"✅ Funções de aplicação de algoritmos definidas!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Análises Específicas do Projeto\n", "\n", "Implementação das análises conforme especificado na documentação."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ========== PADRÕES E TEMPLATES ==========\n", "\n", "# 1. Identificação da empresa (Comgás)\n", "PADRAO_COMGAS = [r\"\\bcon?g[áa]s\\b\", r\"\\bsou\\s+\\w+\\s+da\\s+con?g[áa]s\\b\"]\n", "\n", "# 2. Solicitação de dados pessoais\n", "PADRAO_CPF = [r\"\\bcpf\\b\", r\"\\b\\d{3}\\.\\d{3}\\.\\d{3}-\\d{2}\\b\", r\"\\b\\d{11}\\b\"]\n", "PADRAO_CODIGO = [r\"\\bc[oó]d(igo)?\\b\", r\"\\bid\\b\"]\n", "\n", "# 3. Confirmação de dados\n", "PADRAO_EMAIL = [r\"[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}\"]\n", "PADRAO_TELEFONE = [r\"\\(?\\d{2}\\)?\\s?9?\\d{4}-?\\d{4}\"]\n", "PADRAO_PROTOCOLO = [r\"\\b\\d{6,}-\\d{3}\\b\"]\n", "\n", "# 4. Fraseologias de cordialidade\n", "PADRAO_CORDIALIDADE = [r\"\\bpor favor\\b\", r\"\\bobrigad[oa]\\b\", r\"\\bcom licen[çc]a\\b\"]\n", "\n", "# 5. <PERSON> e retorno\n", "PADRAO_HOLD = [r\"\\binstante\\b\", r\"\\baguarde\\b\", r\"\\bvou verificar\\b\"]\n", "PADRAO_RETORNO = [r\"\\bobrigad[oa] por aguardar\\b\", r\"\\bretornando\\b\"]\n", "\n", "# 6. Templates para embeddings\n", "templates_queda = [\n", "    \"no caso de queda de ligação, posso retornar neste número?\",\n", "    \"se a chamada cair, posso ligar de volta neste número?\",\n", "    \"se cair a ligação, posso retornar para este telefone?\"\n", "]\n", "\n", "templates_resolucao = [\n", "    \"o problema foi resolvido\",\n", "    \"seu pedido está concluído\", \n", "    \"serviço restabelecido\",\n", "    \"está tudo certo\",\n", "    \"problema solucionado\"\n", "]\n", "\n", "# Pré-computar embeddings dos templates\n", "emb_queda = sbert.encode(templates_queda, normalize_embeddings=True)\n", "emb_resolucao = sbert.encode(templates_resolucao, normalize_embeddings=True)\n", "\n", "print(\"✅ Padrões e templates definidos!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Funções de Análise Específicas\n", "\n", "Implementação das análises por categoria conforme documentação."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analisar_abertura_encerramento(fala: dict) -> dict:\n", "    \"\"\"Análise de abertura/encerramento conforme projeto Carbon\"\"\"\n", "    resultado = {}\n", "    \n", "    # Identificação da empresa\n", "    resultado['citou_empresa'] = aplicar_regex(fala, PADRAO_COMGAS)\n", "    \n", "    # Cordialidade básica\n", "    resultado['cordialidade'] = aplicar_regex(fala, PADRAO_CORDIALIDADE)\n", "    \n", "    # Pedido de contato em caso de queda (usando embeddings)\n", "    resultado['perguntou_queda'] = aplicar_embeddings(fala, emb_queda, limiar=0.72)\n", "    \n", "    return resultado\n", "\n", "def analisar_conducao_atendimento(fala: dict) -> dict:\n", "    \"\"\"Análise de condução do atendimento\"\"\"\n", "    resultado = {}\n", "    \n", "    # Solicitação de dados\n", "    resultado['solicitou_cpf'] = aplicar_regex(fala, PADRAO_CPF)\n", "    resultado['solicitou_codigo'] = aplicar_regex(fala, PADRAO_CODIGO)\n", "    \n", "    # Hold e retorno\n", "    resultado['indicou_hold'] = aplicar_regex(fala, PADRAO_HOLD)\n", "    resultado['retornou_hold'] = aplicar_regex(fala, PADRAO_RETORNO)\n", "    \n", "    return resultado\n", "\n", "def analisar_processos_procedimentos(fala: dict) -> dict:\n", "    \"\"\"Análise de processos e procedimentos\"\"\"\n", "    resultado = {}\n", "    \n", "    # Confirmação de dados\n", "    resultado['confirmou_email'] = aplicar_regex(fala, PADRAO_EMAIL)\n", "    resultado['confirmou_telefone'] = aplicar_regex(fala, PADRAO_TELEFONE)\n", "    resultado['forneceu_protocolo'] = aplicar_regex(fala, PADRAO_PROTOCOLO)\n", "    \n", "    return resultado\n", "\n", "def analisar_resolucao(fala: dict) -> dict:\n", "    \"\"\"Análise de resolução do problema\"\"\"\n", "    resultado = {}\n", "    \n", "    # Encerramento positivo (usando embeddings)\n", "    resultado['encerramento_positivo'] = aplicar_embeddings(fala, emb_resolucao, limiar=0.70)\n", "    \n", "    return resultado\n", "\n", "print(\"✅ Funções de análise específicas definidas!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON>\n", "\n", "Implementação do sistema de pontuação (0-5) conforme especificado."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pesos para cada categoria (conforme documentação)\n", "PESOS = {\n", "    \"abertura\": 1.0,\n", "    \"conducao\": 2.0,\n", "    \"processos\": 1.5,\n", "    \"ncg\": 3.0,  # <PERSON><PERSON> Conformidades Graves\n", "    \"resolucao\": 2.0\n", "}\n", "\n", "def calcular_score_categoria(analises: dict, categoria: str) -> float:\n", "    \"\"\"Calcula score para uma categoria específica\"\"\"\n", "    if categoria == \"abertura\":\n", "        # Score baseado em identificação da empresa e cordialidade\n", "        pontos = 0\n", "        if analises.get('citou_empresa', False): pontos += 0.6\n", "        if analises.get('cordialidade', False): pontos += 0.4\n", "        return min(pontos, 1.0)\n", "    \n", "    elif categoria == \"conducao\":\n", "        # Score baseado em solicitação adequada de dados\n", "        pontos = 0\n", "        if analises.get('solicitou_cpf', False): pontos += 0.3\n", "        if analises.get('solicitou_codigo', False): pontos += 0.3\n", "        if analises.get('indicou_hold', False): pontos += 0.2\n", "        if analises.get('retornou_hold', False): pontos += 0.2\n", "        return min(pontos, 1.0)\n", "    \n", "    elif categoria == \"processos\":\n", "        # Score baseado em confirmação de dados\n", "        pontos = 0\n", "        if analises.get('confirmou_email', False): pontos += 0.3\n", "        if analises.get('confirmou_telefone', False): pontos += 0.3\n", "        if analises.get('forneceu_protocolo', False): pontos += 0.4\n", "        return min(pontos, 1.0)\n", "    \n", "    elif categoria == \"ncg\":\n", "        # Score para Não Conformidades Graves (1.0 = sem problemas)\n", "        return 1.0  # Placeholder - seria implementado com detecção de toxicidade\n", "    \n", "    elif categoria == \"resolucao\":\n", "        # Score baseado em encerramento positivo\n", "        return 1.0 if analises.get('encerramento_positivo', False) else 0.5\n", "    \n", "    return 0.0\n", "\n", "def calcular_nota_final(scores_categorias: dict) -> float:\n", "    \"\"\"Calcula nota final ponderada (1-5)\"\"\"\n", "    num = sum(scores_categorias[c] * PESOS[c] for c in scores_categorias)\n", "    den = sum(PESOS.values())\n", "    return round(1 + 4 * (num / den), 2)\n", "\n", "def analisar_fala_completa(fala: dict) -> dict:\n", "    \"\"\"An<PERSON><PERSON><PERSON> completa de uma fala\"\"\"\n", "    # Executar todas as análises\n", "    analise_abertura = analisar_abertura_encerramento(fala)\n", "    analise_conducao = analisar_conducao_atendimento(fala)\n", "    analise_processos = analisar_processos_procedimentos(fala)\n", "    analise_resolucao = analisar_resolucao(fala)\n", "    \n", "    # <PERSON><PERSON><PERSON><PERSON> todas as an<PERSON><PERSON><PERSON>\n", "    todas_analises = {**analise_abertura, **analise_conducao, **analise_processos, **analise_resolucao}\n", "    \n", "    # Calcular scores por categoria\n", "    scores = {\n", "        \"abertura\": calcular_score_categoria(todas_analises, \"abertura\"),\n", "        \"conducao\": calcular_score_categoria(todas_analises, \"conducao\"),\n", "        \"processos\": calcular_score_categoria(todas_analises, \"processos\"),\n", "        \"ncg\": calcular_score_categoria(todas_analises, \"ncg\"),\n", "        \"resolucao\": calcular_score_categoria(todas_analises, \"resolucao\")\n", "    }\n", "    \n", "    # Calcular nota final\n", "    nota_final = calcular_nota_final(scores)\n", "    \n", "    return {\n", "        \"analises_detalhadas\": todas_analises,\n", "        \"scores_por_categoria\": scores,\n", "        \"nota_final\": nota_final,\n", "        \"timestamp\": fala.get(\"timestamp\"),\n", "        \"speaker\": fala.get(\"speaker\"),\n", "        \"texto_original\": fala.get(\"texto_original\")\n", "    }\n", "\n", "print(\"✅ Sistema de scoring implementado!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Teste com Dados de Exemplo\n", "\n", "Testando o sistema completo com uma conversa simulada."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Dados de exemplo baseados no seu código\n", "conversa = [\n", "    \"[00:00] CLIENTE: <PERSON><PERSON><PERSON>, tudo bem?\",\n", "    \"[00:03] ATENDENTE: <PERSON><PERSON> ta<PERSON>, sou a Júlia da Comgás, em que posso ajudar?\",\n", "    \"[00:07] CLIENTE: Gostaria de verificar a minha conta de gás, acho que está com valor errado.\",\n", "    \"[00:12] ATENDENTE: Certo, por favor me informe o seu CPF para localizar o cadastro.\",\n", "    \"[00:15] CLIENTE: <PERSON><PERSON><PERSON>, é 123.456.789-00.\",\n", "    \"[00:20] ATENDENTE: Obrigada. Só um instante por favor, vou estar verificando as informações.\",\n", "    \"[00:45] ATENDENTE: Obrigada por aguardar, encontrei aqui o cadastro.\",\n", "    \"[00:48] ATENDENTE: Confere se o seu e-mail ainda é <EMAIL>?\",\n", "    \"[00:52] CLIENTE: Sim, continua o mesmo.\",\n", "    \"[00:55] ATENDENTE: Ok, já atualizei o sistema. Seu protocolo é 456789-123.\",\n", "    \"[01:00] ATENDENTE: Verifiquei que o valor maior é porque houve um acúmulo de leitura.\",\n", "    \"[01:05] CLIENTE: <PERSON><PERSON><PERSON>, então está tudo certo.\",\n", "    \"[01:08] ATENDENTE: <PERSON><PERSON> me<PERSON>, o problema está resolvido. Deseja aproveitar e ativar o débito automático?\",\n", "    \"[01:12] CLIENTE: <PERSON><PERSON>, obri<PERSON>.\",\n", "    \"[01:15] ATENDENTE: Sem problemas. A Comgás agradece o seu contato, tenha uma boa tarde!\",\n", "    \"[01:18] CLIENTE: <PERSON><PERSON><PERSON>, boa tarde!\"\n", "]\n", "\n", "# Processar a conversa\n", "print(\"🔄 Processando conversa de exemplo...\")\n", "dados = preprocessar_conversa(conversa)\n", "\n", "print(f\"✅ Conversa processada: {len(dados)} falas identificadas\")\n", "print(\"\\n📋 Primeiras 3 falas processadas:\")\n", "for i, fala in enumerate(dados[:3]):\n", "    print(f\"\\n{i+1}. [{fala['timestamp']}] {fala['speaker']}: {fala['texto_original']}\")\n", "    print(f\"   Texto limpo: {fala['preprocessado']['texto_limpo']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. <PERSON><PERSON><PERSON><PERSON> Conversa\n", "\n", "Executando todas as análises e gerando o score final."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> a<PERSON> as falas do atendente\n", "print(\"🔍 Analisando falas do atendente...\")\n", "print(\"=\" * 60)\n", "\n", "resultados_atendente = []\n", "for i, fala in enumerate(dados):\n", "    if fala['speaker'] == 'Atendente':\n", "        resultado = analisar_fala_completa(fala)\n", "        resultados_atendente.append(resultado)\n", "        \n", "        print(f\"\\n📞 Fala {i+1}: [{fala['timestamp']}]\")\n", "        print(f\"💬 Texto: {fala['texto_original']}\")\n", "        print(f\"📊 Nota: {resultado['nota_final']}/5.0\")\n", "        print(f\"📈 Scores por categoria:\")\n", "        for cat, score in resultado['scores_por_categoria'].items():\n", "            print(f\"   • {cat.capitalize()}: {score:.2f}\")\n", "        \n", "        print(f\"🔍 Análises detectadas:\")\n", "        for analise, valor in resultado['analises_detalhadas'].items():\n", "            if valor:\n", "                print(f\"   ✅ {analise}\")\n", "\n", "# Calcular estatísticas gerais\n", "if resultados_atendente:\n", "    notas = [r['nota_final'] for r in resultados_atendente]\n", "    nota_media = sum(notas) / len(notas)\n", "    \n", "    print(\"\\n\" + \"=\" * 60)\n", "    print(\"📊 RESUMO GERAL DA ANÁLISE\")\n", "    print(\"=\" * 60)\n", "    print(f\"🎯 Nota média do atendimento: {nota_media:.2f}/5.0\")\n", "    print(f\"📈 Melhor nota individual: {max(notas):.2f}/5.0\")\n", "    print(f\"📉 Pior nota individual: {min(notas):.2f}/5.0\")\n", "    print(f\"🗣️ Total de falas do atendente analisadas: {len(resultados_atendente)}\")\n", "    \n", "    # Análise de conformidade\n", "    conformidades = []\n", "    for resultado in resultados_atendente:\n", "        analises = resultado['analises_detalhadas']\n", "        if analises.get('citou_empresa'): conformidades.append('Identificação da empresa')\n", "        if analises.get('solicitou_cpf'): conformidades.append('Solicitação de CPF')\n", "        if analises.get('forneceu_protocolo'): conformidades.append('Fornecimento de protocolo')\n", "        if analises.get('encerramento_positivo'): conformidades.append('Encerramento positivo')\n", "    \n", "    print(f\"\\n✅ Conformidades identificadas:\")\n", "    for conf in set(conformidades):\n", "        print(f\"   • {conf}\")\n", "    \n", "    # Classificação final\n", "    if nota_media >= 4.0:\n", "        classificacao = \"EXCELENTE 🌟\"\n", "    elif nota_media >= 3.0:\n", "        classificacao = \"BOM ✅\"\n", "    elif nota_media >= 2.0:\n", "        classificacao = \"REGULAR ⚠️\"\n", "    else:\n", "        classificacao = \"PRECISA MELHORAR ❌\"\n", "    \n", "    print(f\"\\n🏆 Classificação final: {classificacao}\")\n", "    \n", "else:\n", "    print(\"❌ Nenhuma fala do atendente foi encontrada para análise.\")\n", "\n", "print(\"\\n✅ Análise completa finalizada!\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 0}